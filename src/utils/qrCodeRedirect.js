/**
 * 二维码扫码跳转工具
 * 根据扫码环境自动跳转到对应页面
 */

/**
 * 检测是否为微信环境
 * @returns {boolean}
 */
import wx from 'weixin-js-sdk';
import { ElMessage } from 'element-plus'
export function isWechat() {
    try {
        if (typeof navigator === 'undefined') return false
        const ua = navigator.userAgent.toLowerCase()
        return /micromessenger/.test(ua)
    } catch (error) {
        ElMessage.error('检测微信环境时出错:', error)
        return false
    }
}

/**
 * 检测是否为移动端
 * @returns {boolean}
 */
export function isMobile() {
    try {
        if (typeof navigator === 'undefined') return false
        const ua = navigator.userAgent.toLowerCase()
        return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(ua)
    } catch (error) {
        ElMessage.error('检测移动端环境时出错:', error)
        return false
    }
}

/**
 * 检测是否为微信小程序环境
 * @returns {Promise<boolean>}
 */
export function isMiniProgram() {
    return new Promise((resolve) => {
        try {
            // 检查是否在浏览器环境中
            if (typeof window === 'undefined') {
                resolve(false)
                return
            }

            // 检查wx对象是否存在
            if (typeof window.wx !== 'undefined' && window.wx.miniProgram) {
                window.wx.miniProgram.getEnv((res) => {
                    resolve(res.miniprogram || false)
                })
            } else {
                resolve(false)
            }
        } catch (error) {
            alert('检测小程序环境时出错', error)
            ElMessage.error('检测小程序环境时出错:', error)
            resolve(false)
        }
    })
}

/**
 * 跳转到微信小程序
 * @param {string} sn - 设备编号
 * @param {string} appId - 小程序AppId（可选）
 */
export function redirectToMiniProgram(sn, appId) {
    // const path = `packageDevice/detail?sn=${sn}`
    const path = `packageDevice/detail?deviceSn=21881E0003CB&deviceName=%E8%B7%83%E9%A3%9E`

    try {
        // 检查是否在微信小程序环境中
        if (typeof window !== 'undefined' && typeof window.wx !== 'undefined' && window.wx.miniProgram) {
            alert('微信环境，第一步判断是否是小程序')
            // 如果在微信小程序环境中，直接跳转
            window.wx.miniProgram.navigateTo({
                url: `/${path}`,
                success: () => {
                    ElMessage.error('跳转到小程序页面成功')
                },
                fail: (err) => {
                    ElMessage.error('跳转到小程序页面失败:', err)
                }
            })
        } else if (isWechat()) {
            alert('微信环境：判断是否是微信', appId)
            // 如果在微信浏览器中，尝试打开小程序
            if (appId) {
                alert(`appid:${appId}`)
                // 如果有AppId，使用微信开放标签
                const openTag = `
                    <wx-open-launch-weapp
                        id="launch-btn-${Date.now()}"
                        appid="${appId}"
                        path="${path}"
                        style="display: none;"
                    >
                        <template>
                            <button>打开小程序</button>
                        </template>
                    </wx-open-launch-weapp>
                `
                document.body.insertAdjacentHTML('beforeend', openTag)

                // 自动触发点击
                setTimeout(() => {
                    const launchBtn = document.querySelector(`#launch-btn-${Date.now()}`)
                    if (launchBtn) {
                        launchBtn.click()
                    }
                }, 100)
            } else {
                // 降级方案：提示用户手动打开小程序
                alert('请在微信中搜索小程序并打开对应页面')
            }
        } else {
            ElMessage.error('当前环境不支持跳转到微信小程序')
            // 降级到H5页面
            redirectToH5(sn)
        }
    } catch (error) {
        ElMessage.error('跳转到小程序时出错:', error)
        // 降级到H5页面
        redirectToH5(sn)
    }
}

/**
 * 跳转到H5页面
 * @param {string} sn - 设备编号
 * @param {string} baseUrl - 基础URL（可选）
 */
export function redirectToH5(sn, baseUrl = '') {
    const url = `${baseUrl}/#/h5?sn=${sn}`

    if (isMobile()) {
        // 移动端直接跳转
        window.location.href = url
    } else {
        // 桌面端在新窗口打开
        window.open(url, '_blank')
    }
}

/**
 * 智能跳转函数
 * 根据环境自动选择跳转方式
 * @param {string} sn - 设备编号
 * @param {Object} options - 配置选项
 * @param {string} options.miniProgramAppId - 小程序AppId
 * @param {string} options.h5BaseUrl - H5基础URL
 */

export async function smartRedirect(sn, options = {}) {
    const { miniProgramAppId, h5BaseUrl = '' } = options
    alert(miniProgramAppId, h5BaseUrl)
    // ElMessage.error('开始智能跳转，设备编号:', sn)
    // try {
    // 检测是否在小程序环境中
    const inMiniProgram = await isMiniProgram()
    alert('小程序环境:', !!inMiniProgram)
    if (inMiniProgram) {
        ElMessage.error('检测到小程序环境，跳转到小程序页面')
        redirectToMiniProgram(sn, miniProgramAppId)
        return
    }
    // 检测是否为微信环境
    if (isWechat()) {
        ElMessage.error('检测到微信环境，尝试跳转到小程序')
        redirectToMiniProgram(sn, miniProgramAppId)
        return
    }

    // 其他情况跳转到H5页面
    ElMessage.error('跳转到H5页面')
    redirectToH5(sn, h5BaseUrl)
    // } catch (error) {
    //     ElMessage.error('智能跳转失败:', error)
    //     // 降级到H5页面
    //     redirectToH5(sn, h5BaseUrl)
    // }
}

/**
 * 创建二维码跳转页面
 * @param {string} sn - 设备编号
 * @param {Object} options - 配置选项
 */


// 默认导出
export default {
    isWechat,
    isMobile,
    isMiniProgram,
    redirectToMiniProgram,
    redirectToH5,
    smartRedirect,
}
